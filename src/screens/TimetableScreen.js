import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  FlatList,
  Dimensions,
  ScrollView,
  Animated,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { FontAwesomeIcon } from '@fortawesome/react-native-fontawesome';
import {
  faArrowLeft,
  faCalendarAlt,
  faClock,
  faUser,
  faGraduationCap,
  faChevronRight,
  faCircle,
} from '@fortawesome/free-solid-svg-icons';
import timetableData from '../data/dummyTimetable.json';
import { useScreenOrientation } from '../hooks/useScreenOrientation';

const screenWidth = Dimensions.get('window').width;

export default function TimetableScreen({ navigation, route }) {
  const [timetable, setTimetable] = useState(null);
  const [availableDays, setAvailableDays] = useState([
    'Monday',
    'Tuesday',
    'Wednesday',
    'Thursday',
    'Friday',
  ]);
  const { studentName, authCode } = route.params || {};

  // Enable rotation for this screen (optional - you can remove this if you want timetable to stay portrait)
  // useScreenOrientation(true);

  const baseDays = [
    'Monday',
    'Tuesday',
    'Wednesday',
    'Thursday',
    'Friday',
    'Saturday',
  ];

  // Helper function to convert object response to array format
  const convertObjectToArrayFormat = (data) => {
    console.log('Original API Response:', data); // Debug log to see your data structure

    // If data is already in the correct format (object with day keys containing arrays), return as is
    if (
      data &&
      typeof data === 'object' &&
      data.Monday &&
      Array.isArray(data.Monday)
    ) {
      return data;
    }

    // If data is an object that needs conversion to day-based structure
    if (data && typeof data === 'object') {
      const convertedData = {};

      // Handle your specific API format with numeric keys (1=Monday, 2=Tuesday, etc.)
      const dayMapping = {
        1: 'Monday',
        2: 'Tuesday',
        3: 'Wednesday',
        4: 'Thursday',
        5: 'Friday',
        6: 'Saturday',
      };

      // Convert numeric keys to day names
      Object.keys(data).forEach((key) => {
        const dayName = dayMapping[key];
        if (dayName && Array.isArray(data[key])) {
          // Sort by week_time to ensure proper order, then by created_at to get latest first
          const sortedEntries = data[key].sort((a, b) => {
            if (a.week_time === b.week_time) {
              // If same period, sort by created_at (latest first)
              return new Date(b.created_at) - new Date(a.created_at);
            }
            return a.week_time - b.week_time;
          });

          // Remove duplicates - keep only the latest entry for each week_time
          const uniqueEntries = [];
          const seenPeriods = new Set();

          sortedEntries.forEach((item) => {
            if (!seenPeriods.has(item.week_time)) {
              seenPeriods.add(item.week_time);
              uniqueEntries.push(item);
            }
          });

          // Transform the data to match your component's expected format
          convertedData[dayName] = uniqueEntries.map((item) => ({
            subject:
              item.subject?.name ||
              item.subject?.subject_name ||
              'Unknown Subject',
            teacher:
              item.user?.name || item.user?.full_name || 'Unknown Teacher',
            period: item.week_time,
            time: `Period ${item.week_time}`, // You can customize this format
            // Keep original data for reference
            originalData: item,
          }));
        }
      });

      // Determine which days have data and update available days
      const daysWithData = Object.keys(convertedData).filter(
        (day) => convertedData[day] && convertedData[day].length > 0
      );

      // Set available days based on what's in the data
      const finalAvailableDays = baseDays.filter(
        (day) =>
          daysWithData.includes(day) ||
          ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday'].includes(day)
      );

      // Initialize empty arrays for days that don't have data but should be shown
      finalAvailableDays.forEach((day) => {
        if (!convertedData[day]) {
          convertedData[day] = [];
        }
      });

      console.log('Converted Data:', convertedData); // Debug log to see converted structure
      console.log('Available Days:', finalAvailableDays); // Debug log to see available days

      // Update available days state
      setAvailableDays(finalAvailableDays);

      return convertedData;
    }

    return data;
  };

  const fetchTimetable = async () => {
    try {
      console.log('Fetching timetable with authCode:', authCode);
      const url = `https://sis.bfi.edu.mm/mobile-api/get-student-timetable2?authCode=${authCode}`;
      console.log('Request URL:', url);

      const response = await fetch(url, {
        method: 'GET',
        headers: {
          Accept: 'application/json',
          'Content-Type': 'application/json',
        },
      });

      console.log('Response status:', response.status);
      console.log('Response headers:', response.headers);

      if (response.ok) {
        const data = await response.json();
        console.log('Raw response data:', data);

        // Convert object to array format if needed
        const convertedData = convertObjectToArrayFormat(data);
        return convertedData;
      } else {
        console.error(
          'Failed to fetch timetable:',
          response.status,
          response.statusText
        );
        const errorText = await response.text();
        console.error('Error response body:', errorText);
        return null;
      }
    } catch (error) {
      console.error('Error fetching timetable:', error);
      console.error('Error details:', {
        message: error.message,
        name: error.name,
        stack: error.stack,
      });

      // Check if it's a network error
      if (error.message.includes('Network request failed')) {
        console.error('Network Error: This might be due to:');
        console.error('1. No internet connection');
        console.error('2. Server is down');
        console.error(
          '3. Android network security policy blocking HTTP requests'
        );
        console.error('4. Firewall or proxy blocking the request');
      }

      return null;
    }
  };
  useEffect(() => {
    const fetchAndSetTimetable = async () => {
      const data = await fetchTimetable();
      if (data) {
        setTimetable(data);
      }
    };

    fetchAndSetTimetable();
  }, []);

  const getCurrentDay = () => {
    const today = new Date().getDay(); // 0 = Sunday, 1 = Monday, ..., 6 = Saturday
    const dayNames = [
      'Sunday',
      'Monday',
      'Tuesday',
      'Wednesday',
      'Thursday',
      'Friday',
      'Saturday',
    ];
    const currentDayName = dayNames[today];

    // Return current day if it's in available days, otherwise return first available day
    return availableDays.includes(currentDayName)
      ? currentDayName
      : availableDays[0] || 'Monday';
  };

  const [selectedDay, setSelectedDay] = useState('Monday');

  // Update selected day when available days change
  useEffect(() => {
    const currentDay = getCurrentDay();
    setSelectedDay(currentDay);
  }, [availableDays]);

  // Modern gradient colors for periods
  const getPeriodGradient = (period) => {
    const gradients = {
      1: { start: '#FF6B6B', end: '#FF8E8E' }, // Red gradient
      2: { start: '#4ECDC4', end: '#6EDDD6' }, // Teal gradient
      3: { start: '#45B7D1', end: '#67C5DD' }, // Blue gradient
      4: { start: '#96CEB4', end: '#A8D6C2' }, // Green gradient
      5: { start: '#FFEAA7', end: '#FFEFB8' }, // Yellow gradient
      6: { start: '#DDA0DD', end: '#E5B3E5' }, // Purple gradient
      7: { start: '#98D8C8', end: '#A6DFD1' }, // Light Green gradient
      8: { start: '#F7DC6F', end: '#F9E285' }, // Light Yellow gradient
      9: { start: '#BB8FCE', end: '#C7A2D6' }, // Light Purple gradient
      10: { start: '#85C1E9', end: '#9BCAED' }, // Light Blue gradient
    };
    return gradients[period] || { start: '#BDC3C7', end: '#D5DBDB' };
  };

  // Get subject icon based on subject name
  const getSubjectIcon = (subject) => {
    const subjectLower = subject.toLowerCase();

    if (
      subjectLower.includes('math') ||
      subjectLower.includes('algebra') ||
      subjectLower.includes('geometry') ||
      subjectLower.includes('calculus')
    ) {
      return faGraduationCap;
    }
    if (
      subjectLower.includes('science') ||
      subjectLower.includes('physics') ||
      subjectLower.includes('chemistry') ||
      subjectLower.includes('biology')
    ) {
      return faGraduationCap;
    }
    if (subjectLower.includes('english') || subjectLower.includes('language')) {
      return faGraduationCap;
    }
    if (
      subjectLower.includes('history') ||
      subjectLower.includes('geography')
    ) {
      return faGraduationCap;
    }
    if (subjectLower.includes('art') || subjectLower.includes('music')) {
      return faGraduationCap;
    }
    if (subjectLower.includes('pe') || subjectLower.includes('physical')) {
      return faGraduationCap;
    }

    return faGraduationCap; // Default icon
  };

  const renderTimeSlot = ({ item, index }) => {
    const period = item.period || index + 1;
    const gradient = getPeriodGradient(period);
    const isLastItem =
      index ===
      (timetable
        ? timetable[selectedDay]?.length - 1
        : timetableData[selectedDay]?.length - 1);

    return (
      <View style={styles.modernTimeSlotContainer}>
        {/* Timeline connector */}
        <View style={styles.timelineContainer}>
          <View
            style={[styles.timelineDot, { backgroundColor: gradient.start }]}
          >
            <Text style={styles.periodNumber}>{period}</Text>
          </View>
          {!isLastItem && <View style={styles.timelineLine} />}
        </View>

        {/* Subject card */}
        <View
          style={[
            styles.modernSubjectCard,
            { borderLeftColor: gradient.start },
          ]}
        >
          <View style={styles.subjectCardHeader}>
            <View style={styles.subjectInfo}>
              <Text style={styles.modernSubjectText}>{item.subject}</Text>
              <View style={styles.teacherRow}>
                <FontAwesomeIcon icon={faUser} size={14} color='#666' />
                <Text style={styles.modernTeacherText}>{item.teacher}</Text>
              </View>
            </View>
            <View style={styles.periodBadgeContainer}>
              <View
                style={[
                  styles.periodBadge,
                  { backgroundColor: gradient.start },
                ]}
              >
                <FontAwesomeIcon icon={faClock} size={12} color='#fff' />
              </View>
              <FontAwesomeIcon icon={faChevronRight} size={16} color='#ccc' />
            </View>
          </View>

          {/* Subject icon and additional info */}
          <View style={styles.subjectCardFooter}>
            <View
              style={[
                styles.subjectIconContainer,
                { backgroundColor: `${gradient.start}15` },
              ]}
            >
              <FontAwesomeIcon
                icon={getSubjectIcon(item.subject)}
                size={16}
                color={gradient.start}
              />
            </View>
            <Text style={styles.periodLabel}>Period {period}</Text>
          </View>
        </View>
      </View>
    );
  };

  const renderDayTab = (day) => {
    const isSelected = selectedDay === day;
    const dayAbbr = day.substring(0, 3);

    return (
      <TouchableOpacity
        key={day}
        style={[styles.modernDayTab, isSelected && styles.selectedModernDayTab]}
        onPress={() => setSelectedDay(day)}
      >
        <View
          style={[
            styles.dayTabIndicator,
            isSelected && styles.selectedDayTabIndicator,
          ]}
        />
        <Text
          style={[
            styles.modernDayTabText,
            isSelected && styles.selectedModernDayTabText,
          ]}
        >
          {dayAbbr}
        </Text>
        {isSelected && <View style={styles.dayTabGlow} />}
      </TouchableOpacity>
    );
  };

  return (
    <SafeAreaView style={styles.container} edges={['top', 'left', 'right']}>
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <FontAwesomeIcon icon={faArrowLeft} size={20} color='#fff' />
        </TouchableOpacity>
        <View style={styles.headerCenter}>
          <FontAwesomeIcon icon={faCalendarAlt} size={20} color='#fff' />
          <Text style={styles.headerTitle}>Timetable</Text>
        </View>
        <View style={styles.headerRight} />
      </View>

      <View style={styles.content}>
        {/* Modern Day Header */}
        <View style={styles.modernDayHeader}>
          <View style={styles.dayHeaderLeft}>
            <Text style={styles.modernDayTitle}>{selectedDay}</Text>
            <Text style={styles.daySubtitle}>
              {new Date().toLocaleDateString('en-US', {
                month: 'long',
                day: 'numeric',
                year: 'numeric',
              })}
            </Text>
          </View>
          <View style={styles.dayHeaderRight}>
            <View style={styles.scheduleIndicator}>
              <FontAwesomeIcon icon={faCircle} size={8} color='#34C759' />
              <Text style={styles.scheduleIndicatorText}>
                {(timetable
                  ? timetable[selectedDay]
                  : timetableData[selectedDay]
                )?.length || 0}{' '}
                classes
              </Text>
            </View>
          </View>
        </View>

        {/* Timeline Schedule */}
        <ScrollView
          style={styles.modernScheduleContainer}
          showsVerticalScrollIndicator={false}
          contentContainerStyle={styles.modernScheduleList}
        >
          {(timetable ? timetable[selectedDay] : timetableData[selectedDay])
            ?.length > 0 ? (
            (timetable
              ? timetable[selectedDay]
              : timetableData[selectedDay]
            ).map((item, index) => renderTimeSlot({ item, index }))
          ) : (
            <View style={styles.emptyScheduleContainer}>
              <FontAwesomeIcon icon={faCalendarAlt} size={48} color='#ccc' />
              <Text style={styles.emptyScheduleText}>No classes scheduled</Text>
              <Text style={styles.emptyScheduleSubtext}>
                Enjoy your free day!
              </Text>
            </View>
          )}
        </ScrollView>
      </View>

      {/* Day Tabs at Bottom */}
      <View style={styles.bottomTabsContainer}>
        {availableDays.map(renderDayTab)}
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  header: {
    backgroundColor: '#AF52DE',
    padding: 15,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 5,
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  headerCenter: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
    justifyContent: 'center',
  },
  headerTitle: {
    color: '#fff',
    fontSize: 22,
    fontWeight: 'bold',
    marginLeft: 10,
  },
  headerRight: {
    width: 40,
  },
  content: {
    flex: 1,
    padding: 20,
  },

  // Modern Day Header
  modernDayHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 25,
    paddingHorizontal: 5,
  },
  dayHeaderLeft: {
    flex: 1,
  },
  modernDayTitle: {
    fontSize: 32,
    fontWeight: 'bold',
    color: '#1a1a1a',
    marginBottom: 4,
  },
  daySubtitle: {
    fontSize: 16,
    color: '#666',
    fontWeight: '500',
  },
  dayHeaderRight: {
    alignItems: 'flex-end',
  },
  scheduleIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#f0f9ff',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 20,
  },
  scheduleIndicatorText: {
    fontSize: 14,
    color: '#0369a1',
    fontWeight: '600',
    marginLeft: 6,
  },

  // Modern Schedule Container
  modernScheduleContainer: {
    flex: 1,
  },
  modernScheduleList: {
    paddingBottom: 20,
  },

  // Timeline Styles
  modernTimeSlotContainer: {
    flexDirection: 'row',
    marginBottom: 20,
    alignItems: 'flex-start',
  },
  timelineContainer: {
    alignItems: 'center',
    marginRight: 20,
    width: 60,
  },
  timelineDot: {
    width: 50,
    height: 50,
    borderRadius: 25,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  periodNumber: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#fff',
  },
  timelineLine: {
    width: 3,
    height: 40,
    backgroundColor: '#e5e7eb',
    marginTop: 10,
  },

  // Modern Subject Card
  modernSubjectCard: {
    flex: 1,
    backgroundColor: '#fff',
    borderRadius: 16,
    padding: 20,
    borderLeftWidth: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  subjectCardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 15,
  },
  subjectInfo: {
    flex: 1,
    marginRight: 15,
  },
  modernSubjectText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#1a1a1a',
    marginBottom: 8,
  },
  teacherRow: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  modernTeacherText: {
    fontSize: 14,
    color: '#666',
    marginLeft: 6,
    fontWeight: '500',
  },
  periodBadgeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  periodBadge: {
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 8,
  },
  subjectCardFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  subjectIconContainer: {
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: 'center',
    alignItems: 'center',
  },
  periodLabel: {
    fontSize: 12,
    color: '#999',
    fontWeight: '600',
  },

  // Modern Day Tabs
  bottomTabsContainer: {
    flexDirection: 'row',
    backgroundColor: '#fff',
    paddingHorizontal: 20,
    paddingVertical: 20,
    borderTopLeftRadius: 30,
    borderTopRightRadius: 30,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: -4 },
    shadowOpacity: 0.1,
    shadowRadius: 12,
    elevation: 10,
  },
  modernDayTab: {
    flex: 1,
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 8,
    borderRadius: 20,
    marginHorizontal: 4,
    position: 'relative',
  },
  selectedModernDayTab: {
    backgroundColor: '#AF52DE',
    shadowColor: '#AF52DE',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 6,
  },
  dayTabIndicator: {
    width: 4,
    height: 4,
    borderRadius: 2,
    backgroundColor: 'transparent',
    marginBottom: 6,
  },
  selectedDayTabIndicator: {
    backgroundColor: '#fff',
  },
  modernDayTabText: {
    fontSize: 13,
    fontWeight: '600',
    color: '#8E8E93',
  },
  selectedModernDayTabText: {
    color: '#fff',
    fontWeight: '700',
  },
  dayTabGlow: {
    position: 'absolute',
    top: -2,
    left: -2,
    right: -2,
    bottom: -2,
    borderRadius: 22,
    backgroundColor: 'rgba(175, 82, 222, 0.2)',
    zIndex: -1,
  },

  // Empty State
  emptyScheduleContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 60,
  },
  emptyScheduleText: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#666',
    marginTop: 20,
    marginBottom: 8,
  },
  emptyScheduleSubtext: {
    fontSize: 16,
    color: '#999',
    textAlign: 'center',
  },
});
