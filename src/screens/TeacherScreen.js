import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Alert,
  ScrollView,
  Image,
  ActivityIndicator,
  RefreshControl,
  Dimensions,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { FontAwesomeIcon } from '@fortawesome/react-native-fontawesome';
import {
  faUser,
  faCog,
  faSignOutAlt,
  faArrowLeft,
  faCalendarAlt,
  faUsers,
  faGavel,
  faChartLine,
  faCheckCircle,
  faRefresh,
  faBuilding,
  faChevronRight,
} from '@fortawesome/free-solid-svg-icons';
import AsyncStorage from '@react-native-async-storage/async-storage';

const { width: screenWidth } = Dimensions.get('window');

export default function TeacherScreen({ route, navigation }) {
  // Get user data from navigation params or AsyncStorage
  const [userData, setUserData] = useState(route?.params?.userData || {});
  const [loading, setLoading] = useState(true);

  // Teacher dashboard data
  const [timetableData, setTimetableData] = useState(null);
  const [bpsData, setBpsData] = useState(null);
  const [refreshing, setRefreshing] = useState(false);

  const [dashboardStats, setDashboardStats] = useState({
    totalClasses: 0,
    attendanceTaken: 0,
    totalStudents: 0,
    totalBpsRecords: 0,
    branches: 0,
  });

  // Fetch teacher timetable data
  const fetchTeacherTimetable = async () => {
    if (!userData.authCode) return;

    try {
      console.log(
        'Fetching teacher timetable with authCode:',
        userData.authCode
      );
      const url = `https://sis.bfi.edu.mm/mobile-api/get-teacher-timetable-data/?authCode=${userData.authCode}`;

      const response = await fetch(url, {
        method: 'GET',
        headers: {
          Accept: 'application/json',
          'Content-Type': 'application/json',
        },
      });

      if (response.ok) {
        const data = await response.json();
        console.log('Teacher timetable data:', data);
        setTimetableData(data);

        // Calculate stats
        if (data.success && data.branches) {
          const totalClasses = data.branches.reduce(
            (sum, branch) => sum + branch.timetable.length,
            0
          );
          const attendanceTaken = data.branches.reduce(
            (sum, branch) =>
              sum +
              branch.timetable.filter((item) => item.attendance_taken).length,
            0
          );

          setDashboardStats((prev) => ({
            ...prev,
            totalClasses,
            attendanceTaken,
            branches: data.total_branches,
          }));
        }
      } else {
        console.error('Failed to fetch teacher timetable:', response.status);
      }
    } catch (error) {
      console.error('Error fetching teacher timetable:', error);
    }
  };

  // Fetch teacher BPS data
  const fetchTeacherBPS = async () => {
    if (!userData.authCode) return;

    try {
      console.log('Fetching teacher BPS with authCode:', userData.authCode);
      const url = `https://sis.bfi.edu.mm/mobile-api/get-teacher-bps-data/?authCode=${userData.authCode}`;

      const response = await fetch(url, {
        method: 'GET',
        headers: {
          Accept: 'application/json',
          'Content-Type': 'application/json',
        },
      });

      if (response.ok) {
        const data = await response.json();
        console.log('Teacher BPS data:', data);
        setBpsData(data);

        // Calculate stats
        if (data.success && data.branches) {
          const totalStudents = data.branches.reduce(
            (sum, branch) => sum + branch.total_students,
            0
          );
          const totalBpsRecords = data.branches.reduce(
            (sum, branch) => sum + branch.total_bps_records,
            0
          );

          setDashboardStats((prev) => ({
            ...prev,
            totalStudents,
            totalBpsRecords,
          }));
        }
      } else {
        console.error('Failed to fetch teacher BPS:', response.status);
      }
    } catch (error) {
      console.error('Error fetching teacher BPS:', error);
    }
  };

  // Load all teacher data
  const loadTeacherData = async () => {
    setRefreshing(true);
    await Promise.all([fetchTeacherTimetable(), fetchTeacherBPS()]);
    setRefreshing(false);
  };

  useEffect(() => {
    // If no userData from params, try to get from AsyncStorage
    const getUserData = async () => {
      if (Object.keys(userData).length === 0) {
        try {
          const storedUserData = await AsyncStorage.getItem('userData');
          if (storedUserData) {
            const parsedData = JSON.parse(storedUserData);
            // Only set if it's a teacher account
            if (parsedData.userType === 'teacher') {
              setUserData(parsedData);
            } else {
              // If not a teacher account, redirect to home
              navigation.replace('Home');
            }
          }
        } catch (error) {
          console.error('Error retrieving user data:', error);
        }
      }
      setLoading(false);
    };

    getUserData();
    console.log(userData);
  }, [userData]);

  // Load teacher data when userData is available
  useEffect(() => {
    if (userData.authCode && !loading) {
      loadTeacherData();
    }
  }, [userData.authCode, loading]);

  const handleLogout = () => {
    Alert.alert('Logout', 'Are you sure you want to logout?', [
      {
        text: 'Cancel',
        style: 'cancel',
      },
      {
        text: 'Logout',
        onPress: async () => {
          try {
            // Clear user data from AsyncStorage
            await AsyncStorage.removeItem('userData');
            console.log('User logged out successfully');
            // Navigate back to home screen
            navigation.reset({
              index: 0,
              routes: [{ name: 'Home' }],
            });
          } catch (error) {
            console.error('Error logging out:', error);
          }
        },
        style: 'destructive',
      },
    ]);
  };

  return (
    <SafeAreaView style={styles.container} edges={['top', 'left', 'right']}>
      <View style={styles.header}>
        <View style={styles.headerLeft}>
          <TouchableOpacity
            style={styles.headerButton}
            onPress={() => navigation.goBack()}
          >
            <FontAwesomeIcon icon={faArrowLeft} size={20} color='#fff' />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Teacher Dashboard</Text>
        </View>

        <TouchableOpacity style={styles.logoutButton} onPress={handleLogout}>
          <FontAwesomeIcon icon={faSignOutAlt} size={22} color='#fff' />
        </TouchableOpacity>
      </View>

      {loading ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size='large' color='#007AFF' />
          <Text style={styles.loadingText}>Loading teacher dashboard...</Text>
        </View>
      ) : (
        <ScrollView
          style={styles.scrollView}
          refreshControl={
            <RefreshControl
              refreshing={refreshing}
              onRefresh={loadTeacherData}
              colors={['#007AFF']}
              tintColor='#007AFF'
            />
          }
        >
          {/* Teacher Info Header */}
          <View style={styles.teacherInfoHeader}>
            <View style={styles.teacherInfoLeft}>
              <View style={styles.photoContainer}>
                {userData.photo ? (
                  <Image
                    source={{ uri: userData.photo }}
                    style={styles.userPhoto}
                    resizeMode='cover'
                  />
                ) : (
                  <View style={styles.photoPlaceholder}>
                    <FontAwesomeIcon icon={faUser} size={24} color='#007AFF' />
                  </View>
                )}
              </View>
              <View style={styles.teacherInfoText}>
                <Text style={styles.teacherName}>
                  {userData.name || 'Teacher'}
                </Text>
                <Text style={styles.teacherRole}>
                  {userData.position || 'Teacher'} • ID: {userData.id || 'N/A'}
                </Text>
              </View>
            </View>
            <TouchableOpacity
              style={styles.refreshButton}
              onPress={loadTeacherData}
            >
              <FontAwesomeIcon icon={faRefresh} size={18} color='#007AFF' />
            </TouchableOpacity>
          </View>

          {/* Dashboard Stats */}
          <View style={styles.statsContainer}>
            <Text style={styles.sectionTitle}>Dashboard Overview</Text>
            <View style={styles.statsGrid}>
              <View style={styles.statCard}>
                <View
                  style={[
                    styles.statIconContainer,
                    { backgroundColor: '#34C75915' },
                  ]}
                >
                  <FontAwesomeIcon
                    icon={faCalendarAlt}
                    size={20}
                    color='#34C759'
                  />
                </View>
                <Text style={styles.statNumber}>
                  {dashboardStats.totalClasses}
                </Text>
                <Text style={styles.statLabel}>Total Classes</Text>
              </View>

              <View style={styles.statCard}>
                <View
                  style={[
                    styles.statIconContainer,
                    { backgroundColor: '#007AFF15' },
                  ]}
                >
                  <FontAwesomeIcon
                    icon={faCheckCircle}
                    size={20}
                    color='#007AFF'
                  />
                </View>
                <Text style={styles.statNumber}>
                  {dashboardStats.attendanceTaken}
                </Text>
                <Text style={styles.statLabel}>Attendance Taken</Text>
              </View>

              <View style={styles.statCard}>
                <View
                  style={[
                    styles.statIconContainer,
                    { backgroundColor: '#FF950015' },
                  ]}
                >
                  <FontAwesomeIcon icon={faUsers} size={20} color='#FF9500' />
                </View>
                <Text style={styles.statNumber}>
                  {dashboardStats.totalStudents}
                </Text>
                <Text style={styles.statLabel}>Total Students</Text>
              </View>

              <View style={styles.statCard}>
                <View
                  style={[
                    styles.statIconContainer,
                    { backgroundColor: '#AF52DE15' },
                  ]}
                >
                  <FontAwesomeIcon icon={faGavel} size={20} color='#AF52DE' />
                </View>
                <Text style={styles.statNumber}>
                  {dashboardStats.totalBpsRecords}
                </Text>
                <Text style={styles.statLabel}>BPS Records</Text>
              </View>
            </View>
          </View>

          {/* Quick Actions */}
          <View style={styles.quickActionsContainer}>
            <Text style={styles.sectionTitle}>Quick Actions</Text>
            <View style={styles.actionGrid}>
              <TouchableOpacity
                style={styles.actionCard}
                onPress={() =>
                  navigation.navigate('TeacherTimetable', {
                    authCode: userData.authCode,
                    teacherName: userData.name,
                    timetableData: timetableData,
                  })
                }
              >
                <View
                  style={[
                    styles.actionIconContainer,
                    { backgroundColor: '#34C75915' },
                  ]}
                >
                  <FontAwesomeIcon
                    icon={faCalendarAlt}
                    size={24}
                    color='#34C759'
                  />
                </View>
                <Text style={styles.actionTitle}>My Timetable</Text>
                <Text style={styles.actionSubtitle}>
                  View schedule & take attendance
                </Text>
                <View style={styles.actionBadge}>
                  <Text style={styles.actionBadgeText}>
                    {dashboardStats.totalClasses -
                      dashboardStats.attendanceTaken}{' '}
                    pending
                  </Text>
                </View>
              </TouchableOpacity>

              <TouchableOpacity
                style={styles.actionCard}
                onPress={() =>
                  navigation.navigate('TeacherBPS', {
                    authCode: userData.authCode,
                    teacherName: userData.name,
                    bpsData: bpsData,
                  })
                }
              >
                <View
                  style={[
                    styles.actionIconContainer,
                    { backgroundColor: '#AF52DE15' },
                  ]}
                >
                  <FontAwesomeIcon icon={faGavel} size={24} color='#AF52DE' />
                </View>
                <Text style={styles.actionTitle}>BPS Management</Text>
                <Text style={styles.actionSubtitle}>
                  Manage student behavior points
                </Text>
                <View style={styles.actionBadge}>
                  <Text style={styles.actionBadgeText}>
                    {dashboardStats.totalBpsRecords} records
                  </Text>
                </View>
              </TouchableOpacity>
            </View>
          </View>

          {/* Additional Features */}
          <View style={styles.featuresContainer}>
            <Text style={styles.sectionTitle}>Additional Features</Text>
            <View style={styles.featuresList}>
              <TouchableOpacity style={styles.featureItem}>
                <View
                  style={[
                    styles.featureIconContainer,
                    { backgroundColor: '#007AFF15' },
                  ]}
                >
                  <FontAwesomeIcon icon={faUser} size={20} color='#007AFF' />
                </View>
                <View style={styles.featureContent}>
                  <Text style={styles.featureTitle}>My Profile</Text>
                  <Text style={styles.featureSubtitle}>
                    View and edit profile information
                  </Text>
                </View>
                <FontAwesomeIcon icon={faChevronRight} size={16} color='#ccc' />
              </TouchableOpacity>

              <TouchableOpacity style={styles.featureItem}>
                <View
                  style={[
                    styles.featureIconContainer,
                    { backgroundColor: '#FF950015' },
                  ]}
                >
                  <FontAwesomeIcon
                    icon={faChartLine}
                    size={20}
                    color='#FF9500'
                  />
                </View>
                <View style={styles.featureContent}>
                  <Text style={styles.featureTitle}>Analytics</Text>
                  <Text style={styles.featureSubtitle}>
                    View teaching performance metrics
                  </Text>
                </View>
                <FontAwesomeIcon icon={faChevronRight} size={16} color='#ccc' />
              </TouchableOpacity>

              <TouchableOpacity style={styles.featureItem}>
                <View
                  style={[
                    styles.featureIconContainer,
                    { backgroundColor: '#5856D615' },
                  ]}
                >
                  <FontAwesomeIcon icon={faCog} size={20} color='#5856D6' />
                </View>
                <View style={styles.featureContent}>
                  <Text style={styles.featureTitle}>Settings</Text>
                  <Text style={styles.featureSubtitle}>
                    App preferences and notifications
                  </Text>
                </View>
                <FontAwesomeIcon icon={faChevronRight} size={16} color='#ccc' />
              </TouchableOpacity>
            </View>
          </View>

          {/* Branch Information */}
          {timetableData?.branches && timetableData.branches.length > 0 && (
            <View style={styles.branchContainer}>
              <Text style={styles.sectionTitle}>Branch Information</Text>
              {timetableData.branches.map((branch) => (
                <View key={branch.branch_id} style={styles.branchCard}>
                  <View style={styles.branchHeader}>
                    <View
                      style={[
                        styles.branchIconContainer,
                        { backgroundColor: '#34C75915' },
                      ]}
                    >
                      <FontAwesomeIcon
                        icon={faBuilding}
                        size={18}
                        color='#34C759'
                      />
                    </View>
                    <View style={styles.branchInfo}>
                      <Text style={styles.branchName}>
                        {branch.branch_name}
                      </Text>
                      <Text style={styles.branchDetails}>
                        Academic Year: {branch.academic_year_id} • Week:{' '}
                        {branch.current_week}
                      </Text>
                    </View>
                  </View>
                  <View style={styles.branchStats}>
                    <View style={styles.branchStat}>
                      <Text style={styles.branchStatNumber}>
                        {branch.timetable.length}
                      </Text>
                      <Text style={styles.branchStatLabel}>Classes</Text>
                    </View>
                    <View style={styles.branchStat}>
                      <Text style={styles.branchStatNumber}>
                        {
                          branch.timetable.filter(
                            (item) => item.attendance_taken
                          ).length
                        }
                      </Text>
                      <Text style={styles.branchStatLabel}>Attended</Text>
                    </View>
                  </View>
                </View>
              ))}
            </View>
          )}
        </ScrollView>
      )}
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  header: {
    backgroundColor: '#007AFF',
    padding: 15,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 5,
  },
  headerLeft: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  headerButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  headerTitle: {
    color: '#fff',
    fontSize: 22,
    fontWeight: 'bold',
  },
  logoutButton: {
    paddingHorizontal: 10,
  },

  // Loading
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f8f9fa',
  },
  loadingText: {
    marginTop: 15,
    fontSize: 16,
    color: '#666',
    fontWeight: '500',
  },

  // Scroll View
  scrollView: {
    flex: 1,
  },

  // Teacher Info Header
  teacherInfoHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: '#fff',
    margin: 20,
    marginBottom: 15,
    padding: 20,
    borderRadius: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  teacherInfoLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  photoContainer: {
    marginRight: 15,
  },
  userPhoto: {
    width: 50,
    height: 50,
    borderRadius: 25,
  },
  photoPlaceholder: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: '#f0f0f0',
    justifyContent: 'center',
    alignItems: 'center',
  },
  teacherInfoText: {
    flex: 1,
  },
  teacherName: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#1a1a1a',
    marginBottom: 4,
  },
  teacherRole: {
    fontSize: 14,
    color: '#666',
    fontWeight: '500',
  },
  refreshButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#f0f9ff',
    justifyContent: 'center',
    alignItems: 'center',
  },

  // Stats Container
  statsContainer: {
    marginHorizontal: 20,
    marginBottom: 25,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#1a1a1a',
    marginBottom: 15,
  },
  statsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  statCard: {
    width: (screenWidth - 60) / 2,
    backgroundColor: '#fff',
    borderRadius: 16,
    padding: 20,
    marginBottom: 15,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  statIconContainer: {
    width: 50,
    height: 50,
    borderRadius: 25,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 12,
  },
  statNumber: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#1a1a1a',
    marginBottom: 4,
  },
  statLabel: {
    fontSize: 14,
    color: '#666',
    fontWeight: '500',
    textAlign: 'center',
  },

  // Quick Actions
  quickActionsContainer: {
    marginHorizontal: 20,
    marginBottom: 25,
  },
  actionGrid: {
    gap: 15,
  },
  actionCard: {
    backgroundColor: '#fff',
    borderRadius: 16,
    padding: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  actionIconContainer: {
    width: 50,
    height: 50,
    borderRadius: 25,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 15,
  },
  actionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#1a1a1a',
    marginBottom: 6,
  },
  actionSubtitle: {
    fontSize: 14,
    color: '#666',
    marginBottom: 12,
  },
  actionBadge: {
    backgroundColor: '#f0f9ff',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 20,
    alignSelf: 'flex-start',
  },
  actionBadgeText: {
    fontSize: 12,
    color: '#0369a1',
    fontWeight: '600',
  },

  // Features
  featuresContainer: {
    marginHorizontal: 20,
    marginBottom: 25,
  },
  featuresList: {
    backgroundColor: '#fff',
    borderRadius: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  featureItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  featureIconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 15,
  },
  featureContent: {
    flex: 1,
  },
  featureTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1a1a1a',
    marginBottom: 4,
  },
  featureSubtitle: {
    fontSize: 14,
    color: '#666',
  },

  // Branch Information
  branchContainer: {
    marginHorizontal: 20,
    marginBottom: 25,
  },
  branchCard: {
    backgroundColor: '#fff',
    borderRadius: 16,
    padding: 20,
    marginBottom: 15,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  branchHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 15,
  },
  branchIconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 15,
  },
  branchInfo: {
    flex: 1,
  },
  branchName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#1a1a1a',
    marginBottom: 4,
  },
  branchDetails: {
    fontSize: 14,
    color: '#666',
  },
  branchStats: {
    flexDirection: 'row',
    justifyContent: 'space-around',
  },
  branchStat: {
    alignItems: 'center',
  },
  branchStatNumber: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#1a1a1a',
    marginBottom: 4,
  },
  branchStatLabel: {
    fontSize: 12,
    color: '#666',
    fontWeight: '500',
  },
});
