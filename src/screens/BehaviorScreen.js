import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  FlatList,
  ScrollView,
  Dimensions,
  Alert,
  ActivityIndicator,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { FontAwesomeIcon } from '@fortawesome/react-native-fontawesome';
import {
  faArrowLeft,
  faGavel,
  faUser,
  faClipboardList,
  faStar,
  faAward,
  faThumbsUp,
  faThumbsDown,
} from '@fortawesome/free-solid-svg-icons';

export default function BehaviorScreen({ navigation, route }) {
  const [screenData, setScreenData] = useState(Dimensions.get('window'));
  const { authCode } = route.params || {};
  const [behaviorData, setBehaviorData] = useState([]);
  const [loading, setLoading] = useState(true);
  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 10;

  const isLandscape = screenData.width > screenData.height;

  useEffect(() => {
    const subscription = Dimensions.addEventListener('change', ({ window }) => {
      setScreenData(window);
    });

    return () => subscription?.remove();
  }, []);

  useEffect(() => {
    if (authCode) {
      fetchBehaviorData();
    }
  }, [authCode]);

  // Helper function to get behavior type icon and color
  const getBehaviorTypeInfo = (type, points) => {
    const typeCode = type?.toUpperCase() || '';
    const pointValue = parseInt(points) || 0;

    // Handle API type codes: PRS = Positive, DPS = Negative
    if (
      typeCode === 'PRS' ||
      (typeCode.includes('POSITIVE') && pointValue > 0)
    ) {
      return {
        icon: faThumbsUp,
        color: '#34C759',
        bgColor: '#34C75915',
        label: 'Positive',
      };
    } else if (
      typeCode === 'DPS' ||
      (typeCode.includes('NEGATIVE') && pointValue < 0)
    ) {
      return {
        icon: faThumbsDown,
        color: '#FF3B30',
        bgColor: '#FF3B3015',
        label: 'Negative',
      };
    } else if (typeCode.includes('ACHIEVEMENT') || typeCode.includes('AWARD')) {
      return {
        icon: faAward,
        color: '#FF9500',
        bgColor: '#FF950015',
        label: 'Achievement',
      };
    } else {
      return {
        icon: faClipboardList,
        color: '#007AFF',
        bgColor: '#007AFF15',
        label: 'Neutral',
      };
    }
  };

  const fetchBehaviorData = async () => {
    if (!authCode) {
      Alert.alert('Error', 'Authentication code is missing');
      return;
    }

    setLoading(true);
    try {
      console.log('Fetching behavior data with authCode:', authCode);
      const url = `https://sis.bfi.edu.mm/mobile-api/get-student-bps-data?authCode=${authCode}`;
      console.log('Request URL:', url);

      const response = await fetch(url, {
        method: 'GET',
        headers: {
          Accept: 'application/json',
          'Content-Type': 'application/json',
        },
      });

      console.log('Response status:', response.status);

      if (response.ok) {
        const data = await response.json();
        console.log('Raw behavior data:', JSON.stringify(data, null, 2));

        // Handle different possible API response structures
        let behaviorArray = [];
        if (Array.isArray(data)) {
          behaviorArray = data;
        } else if (data && data.data && Array.isArray(data.data)) {
          behaviorArray = data.data;
        } else if (data && typeof data === 'object') {
          // If data is an object, try to find an array property
          const possibleArrays = Object.values(data).filter((val) =>
            Array.isArray(val)
          );
          if (possibleArrays.length > 0) {
            behaviorArray = possibleArrays[0];
          }
        }

        setBehaviorData(behaviorArray);
      } else {
        console.error(
          'Failed to fetch behavior data:',
          response.status,
          response.statusText
        );
        const errorText = await response.text();
        console.error('Error response body:', errorText);

        // For development, use dummy data if API fails
        setBehaviorData(getDummyBehaviorData());
      }
    } catch (error) {
      console.error('Error fetching behavior data:', error);

      // For development, use dummy data if API fails
      setBehaviorData(getDummyBehaviorData());
    } finally {
      setLoading(false);
    }
  };

  // Dummy data for development (matching API format)
  const getDummyBehaviorData = () => [
    {
      id: 52251,
      item_title: 'Excellent Class Performance',
      item_type: 'PRS',
      item_point: 1,
      date: '2025-01-15',
      note: '',
      teacher_name: 'Ms. Johnson',
      status: 1,
    },
    {
      id: 52252,
      item_title: 'Outstanding Project Presentation',
      item_type: 'PRS',
      item_point: 2,
      date: '2025-01-12',
      note: 'Excellent work on science project',
      teacher_name: 'Dr. Smith',
      status: 1,
    },
    {
      id: 52253,
      item_title: 'Late Homework Submission',
      item_type: 'DPS',
      item_point: -1,
      date: '2025-01-10',
      note: 'Assignment submitted 2 days late',
      teacher_name: 'Mr. Brown',
      status: 1,
    },
    {
      id: 52254,
      item_title: 'Helping Classmates',
      item_type: 'PRS',
      item_point: 1,
      date: '2025-01-08',
      note: 'Assisted struggling students with math problems',
      teacher_name: 'Ms. Davis',
      status: 1,
    },
    {
      id: 52255,
      item_title: 'Disruptive Behavior',
      item_type: 'DPS',
      item_point: -2,
      date: '2025-01-05',
      note: 'Talking during class instruction',
      teacher_name: 'Dr. Wilson',
      status: 1,
    },
  ];

  // Helper function to format date
  const formatDate = (dateString) => {
    if (!dateString) return 'No date';
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric',
    });
  };

  // Calculate total points
  const getTotalPoints = () => {
    return behaviorData.reduce((total, item) => {
      const points = parseInt(item.item_point || item.points) || 0;
      return total + points;
    }, 0);
  };

  // Get behavior statistics
  const getBehaviorStats = () => {
    const positive = behaviorData.reduce((total, item) => {
      const points = parseInt(item.item_point || item.points) || 0;
      const type = (item.item_type || item.type || '').toUpperCase();
      if (type === 'PRS' || points > 0) {
        return total + points;
      }
      return total;
    }, 0);

    const negative = behaviorData.reduce((total, item) => {
      const points = parseInt(item.item_point || item.points) || 0;
      const type = (item.item_type || item.type || '').toUpperCase();
      if (type === 'DPS' || points < 0) {
        return total + Math.abs(points); // Use absolute value for display
      }
      return total;
    }, 0);

    const neutral = behaviorData.filter((item) => {
      const points = parseInt(item.item_point || item.points) || 0;
      const type = (item.item_type || item.type || '').toUpperCase();
      return type !== 'PRS' && type !== 'DPS' && points === 0;
    }).length;

    return { positive, negative, neutral, total: behaviorData.length };
  };

  // Pagination logic
  const totalPages = Math.ceil(behaviorData.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const paginatedData = behaviorData.slice(startIndex, endIndex);

  const goToNextPage = () => {
    if (currentPage < totalPages) {
      setCurrentPage(currentPage + 1);
    }
  };

  const goToPreviousPage = () => {
    if (currentPage > 1) {
      setCurrentPage(currentPage - 1);
    }
  };

  return (
    <SafeAreaView style={styles.container} edges={['top', 'left', 'right']}>
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <FontAwesomeIcon icon={faArrowLeft} size={18} color='#fff' />
        </TouchableOpacity>
        <View style={styles.headerCenter}>
          <FontAwesomeIcon icon={faGavel} size={20} color='#fff' />
          <Text style={styles.headerTitle}>Behavior Points</Text>
        </View>
        <View style={styles.headerRight} />
      </View>

      <View style={[styles.content, isLandscape && styles.landscapeContent]}>
        {loading ? (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size='large' color='#5856D6' />
            <Text style={styles.loadingText}>Loading behavior data...</Text>
          </View>
        ) : (
          <ScrollView
            style={styles.scrollContainer}
            showsVerticalScrollIndicator={false}
          >
            {/* Summary Cards */}
            <View style={styles.summaryContainer}>
              <View style={styles.summaryCard}>
                <View style={styles.summaryHeader}>
                  <FontAwesomeIcon icon={faStar} size={24} color='#FF9500' />
                  <Text style={styles.summaryTitle}>Total Points</Text>
                </View>
                <Text
                  style={[
                    styles.summaryValue,
                    { color: getTotalPoints() >= 0 ? '#34C759' : '#FF3B30' },
                  ]}
                >
                  {getTotalPoints()}
                </Text>
              </View>

              <View style={styles.summaryCard}>
                <View style={styles.summaryHeader}>
                  <FontAwesomeIcon
                    icon={faClipboardList}
                    size={24}
                    color='#007AFF'
                  />
                  <Text style={styles.summaryTitle}>Total Records</Text>
                </View>
                <Text style={styles.summaryValue}>{behaviorData.length}</Text>
              </View>
            </View>

            {/* Statistics */}
            <View style={styles.statsContainer}>
              <Text style={styles.sectionTitle}>Behavior Statistics</Text>
              <View style={styles.statsGrid}>
                <View style={styles.statItem}>
                  <View
                    style={[styles.statBadge, { backgroundColor: '#34C759' }]}
                  >
                    <FontAwesomeIcon icon={faThumbsUp} size={16} color='#fff' />
                  </View>
                  <Text style={styles.statNumber}>
                    {getBehaviorStats().positive}
                  </Text>
                  <Text style={styles.statLabel}>PRS</Text>
                </View>

                <View style={styles.statItem}>
                  <View
                    style={[styles.statBadge, { backgroundColor: '#FF3B30' }]}
                  >
                    <FontAwesomeIcon
                      icon={faThumbsDown}
                      size={16}
                      color='#fff'
                    />
                  </View>
                  <Text style={styles.statNumber}>
                    {getBehaviorStats().negative}
                  </Text>
                  <Text style={styles.statLabel}>DPS</Text>
                </View>
              </View>
            </View>

            {/* Behavior Records */}
            <View style={styles.recordsContainer}>
              <Text style={styles.sectionTitle}>Recent Records</Text>

              {behaviorData.length === 0 ? (
                <View style={styles.emptyContainer}>
                  <FontAwesomeIcon icon={faGavel} size={48} color='#8E8E93' />
                  <Text style={styles.emptyText}>
                    No behavior records found
                  </Text>
                  <Text style={styles.emptySubtext}>
                    Behavior records will appear here once available
                  </Text>
                </View>
              ) : (
                <>
                  <FlatList
                    data={paginatedData}
                    renderItem={({ item, index }) => {
                      const typeInfo = getBehaviorTypeInfo(
                        item.item_type || item.type,
                        item.item_point || item.points
                      );

                      return (
                        <View
                          style={[
                            styles.behaviorCard,
                            index % 2 === 0 && styles.evenCard,
                          ]}
                        >
                          <View style={styles.cardHeader}>
                            <View style={styles.cardLeft}>
                              <View
                                style={[
                                  styles.typeIconContainer,
                                  { backgroundColor: typeInfo.bgColor },
                                ]}
                              >
                                <FontAwesomeIcon
                                  icon={typeInfo.icon}
                                  size={20}
                                  color={typeInfo.color}
                                />
                              </View>
                              <View style={styles.cardInfo}>
                                <Text style={styles.cardReason}>
                                  {item.item_title ||
                                    item.reason ||
                                    'No reason provided'}
                                </Text>
                                <Text style={styles.cardDate}>
                                  {formatDate(item.date)}
                                </Text>
                              </View>
                            </View>
                            <View style={styles.cardRight}>
                              <View
                                style={[
                                  styles.pointsBadge,
                                  { backgroundColor: typeInfo.color },
                                ]}
                              >
                                <Text style={styles.pointsText}>
                                  {parseInt(item.item_point || item.points) > 0
                                    ? '+'
                                    : ''}
                                  {item.item_point || item.points || 0}
                                </Text>
                              </View>
                            </View>
                          </View>

                          <View style={styles.cardBody}>
                            <View style={styles.cardDetails}>
                              {(item.teacher_name || item.teacher) && (
                                <View style={styles.cardDetailItem}>
                                  <FontAwesomeIcon
                                    icon={faUser}
                                    size={14}
                                    color='#666'
                                  />
                                  <Text style={styles.cardDetailText}>
                                    {item.teacher_name || item.teacher}
                                  </Text>
                                </View>
                              )}
                              {item.note && item.note.trim() && (
                                <View style={styles.cardDetailItem}>
                                  <FontAwesomeIcon
                                    icon={faClipboardList}
                                    size={14}
                                    color='#666'
                                  />
                                  <Text style={styles.cardDetailText}>
                                    {item.note}
                                  </Text>
                                </View>
                              )}
                            </View>

                            <View style={styles.cardTypeContainer}>
                              <View
                                style={[
                                  styles.typeBadge,
                                  { backgroundColor: typeInfo.color },
                                ]}
                              >
                                <Text style={styles.typeText}>
                                  {typeInfo.label}
                                </Text>
                              </View>
                            </View>
                          </View>
                        </View>
                      );
                    }}
                    keyExtractor={(item) =>
                      item.id?.toString() || Math.random().toString()
                    }
                    showsVerticalScrollIndicator={false}
                    scrollEnabled={false}
                  />

                  {/* Pagination */}
                  {totalPages > 1 && (
                    <View style={styles.paginationContainer}>
                      <TouchableOpacity
                        style={[
                          styles.paginationButton,
                          currentPage === 1 && styles.disabledButton,
                        ]}
                        onPress={goToPreviousPage}
                        disabled={currentPage === 1}
                      >
                        <Text
                          style={[
                            styles.paginationButtonText,
                            currentPage === 1 && styles.disabledButtonText,
                          ]}
                        >
                          Previous
                        </Text>
                      </TouchableOpacity>

                      <Text style={styles.paginationInfo}>
                        Page {currentPage} of {totalPages}
                      </Text>

                      <TouchableOpacity
                        style={[
                          styles.paginationButton,
                          currentPage === totalPages && styles.disabledButton,
                        ]}
                        onPress={goToNextPage}
                        disabled={currentPage === totalPages}
                      >
                        <Text
                          style={[
                            styles.paginationButtonText,
                            currentPage === totalPages &&
                              styles.disabledButtonText,
                          ]}
                        >
                          Next
                        </Text>
                      </TouchableOpacity>
                    </View>
                  )}
                </>
              )}
            </View>
          </ScrollView>
        )}
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  header: {
    backgroundColor: '#5856D6',
    padding: 15,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  backButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  headerCenter: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
    justifyContent: 'center',
  },
  headerTitle: {
    color: '#fff',
    fontSize: 20,
    fontWeight: 'bold',
    marginLeft: 8,
  },
  headerRight: {
    width: 36,
    justifyContent: 'center',
    alignItems: 'center',
  },
  content: {
    flex: 1,
    padding: 15,
  },
  landscapeContent: {
    paddingHorizontal: 20,
  },
  scrollContainer: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: '#666',
  },

  // Summary Cards
  summaryContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 20,
  },
  summaryCard: {
    backgroundColor: '#fff',
    borderRadius: 16,
    padding: 20,
    width: '48%',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  summaryHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 10,
  },
  summaryTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginLeft: 10,
  },
  summaryValue: {
    fontSize: 32,
    fontWeight: 'bold',
    textAlign: 'center',
  },

  // Statistics
  statsContainer: {
    marginBottom: 20,
  },
  sectionTitle: {
    fontSize: 22,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 15,
  },
  statsGrid: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    backgroundColor: '#fff',
    borderRadius: 16,
    padding: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  statItem: {
    alignItems: 'center',
  },
  statBadge: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 8,
  },
  statNumber: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 4,
  },
  statLabel: {
    fontSize: 12,
    color: '#666',
    fontWeight: '600',
  },

  // Records
  recordsContainer: {
    marginBottom: 20,
  },

  // Behavior Cards
  behaviorCard: {
    backgroundColor: '#fff',
    borderRadius: 16,
    padding: 20,
    marginBottom: 15,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  evenCard: {
    backgroundColor: '#fafafa',
  },
  cardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 15,
  },
  cardLeft: {
    flexDirection: 'row',
    flex: 1,
    marginRight: 15,
  },
  typeIconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  cardInfo: {
    flex: 1,
  },
  cardReason: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 4,
  },
  cardDate: {
    fontSize: 14,
    color: '#666',
  },
  cardRight: {
    alignItems: 'flex-end',
  },
  pointsBadge: {
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 12,
    minWidth: 50,
    alignItems: 'center',
  },
  pointsText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#fff',
  },
  cardBody: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  cardDetails: {
    flex: 1,
  },
  cardDetailItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  cardDetailText: {
    fontSize: 14,
    color: '#666',
    marginLeft: 8,
  },
  cardTypeContainer: {
    alignItems: 'flex-end',
  },
  typeBadge: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 20,
  },
  typeText: {
    fontSize: 12,
    fontWeight: '600',
    color: '#fff',
  },

  // Empty State
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 40,
    paddingVertical: 60,
  },
  emptyText: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
    marginTop: 20,
    marginBottom: 10,
    textAlign: 'center',
  },
  emptySubtext: {
    fontSize: 14,
    color: '#666',
    textAlign: 'center',
    lineHeight: 20,
  },

  // Pagination
  paginationContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 20,
    paddingHorizontal: 10,
  },
  paginationButton: {
    backgroundColor: '#5856D6',
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 8,
  },
  disabledButton: {
    backgroundColor: '#ccc',
  },
  paginationButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
  disabledButtonText: {
    color: '#999',
  },
  paginationInfo: {
    fontSize: 16,
    color: '#333',
    fontWeight: '600',
  },
});
